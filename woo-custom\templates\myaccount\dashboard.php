<?php
/**
 * My Account Dashboard - Custom Enhanced Version
 *
 * @package WooCustom
 */

defined('ABSPATH') || exit;

$current_user = wp_get_current_user();
$customer = new WC_Customer(get_current_user_id());

// Get recent orders
$recent_orders = wc_get_orders(array(
    'customer' => get_current_user_id(),
    'limit' => 3,
    'status' => array('wc-completed', 'wc-processing', 'wc-on-hold')
));

// Get wishlist count
$wishlist = WooCustom_Wishlist::instance();
$wishlist_count = $wishlist->get_wishlist_count();
$recent_wishlist_products = $wishlist->get_wishlist_products(3);

// Get reviews data
$reviews_handler = WooCustom_Reviews::instance();
$reviews_count = $reviews_handler->get_user_reviews_count();
$average_rating = $reviews_handler->get_user_average_rating();
$recent_reviews = $reviews_handler->get_user_reviews(3);

$allowed_html = array(
    'a' => array(
        'href' => array(),
    ),
);
?>

<div class="woo-custom-dashboard">
    <!-- Welcome Section -->
    <div class="dashboard-welcome">
        <h2><?php printf(esc_html__('Merhaba, %s!', 'woo-custom'), '<strong>' . esc_html($current_user->display_name) . '</strong>'); ?></h2>
        <p class="welcome-text">
            <?php esc_html_e('Hesap panonuzdan siparişlerinizi görüntüleyebilir, istek listenizi yönetebilir ve değerlendirmelerinizi takip edebilirsiniz.', 'woo-custom'); ?>
        </p>
    </div>

    <!-- Quick Stats -->
    <div class="dashboard-stats">
        <div class="stat-card orders-stat">
            <div class="stat-icon">📦</div>
            <div class="stat-content">
                <div class="stat-number"><?php echo count($recent_orders); ?></div>
                <div class="stat-label"><?php esc_html_e('Son Siparişler', 'woo-custom'); ?></div>
            </div>
            <a href="<?php echo esc_url(wc_get_account_endpoint_url('orders')); ?>" class="stat-link">
                <?php esc_html_e('Tümünü Gör', 'woo-custom'); ?>
            </a>
        </div>

        <div class="stat-card wishlist-stat">
            <div class="stat-icon">♡</div>
            <div class="stat-content">
                <div class="stat-number"><?php echo esc_html($wishlist_count); ?></div>
                <div class="stat-label"><?php esc_html_e('İstek Listesi', 'woo-custom'); ?></div>
            </div>
            <a href="<?php echo esc_url(wc_get_account_endpoint_url('wishlist')); ?>" class="stat-link">
                <?php esc_html_e('Listemi Gör', 'woo-custom'); ?>
            </a>
        </div>

        <div class="stat-card reviews-stat">
            <div class="stat-icon">⭐</div>
            <div class="stat-content">
                <div class="stat-number"><?php echo esc_html($reviews_count); ?></div>
                <div class="stat-label"><?php esc_html_e('Değerlendirmeler', 'woo-custom'); ?></div>
            </div>
            <a href="<?php echo esc_url(wc_get_account_endpoint_url('my-reviews')); ?>" class="stat-link">
                <?php esc_html_e('Değerlendirmelerim', 'woo-custom'); ?>
            </a>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="dashboard-sections">
        <!-- Recent Orders -->
        <?php if (!empty($recent_orders)) : ?>
        <div class="dashboard-section recent-orders">
            <div class="section-header">
                <h3><?php esc_html_e('Son Siparişlerim', 'woo-custom'); ?></h3>
                <a href="<?php echo esc_url(wc_get_account_endpoint_url('orders')); ?>" class="view-all-link">
                    <?php esc_html_e('Tümünü Gör', 'woo-custom'); ?>
                </a>
            </div>
            <div class="orders-list">
                <?php foreach ($recent_orders as $order) : ?>
                <div class="order-item">
                    <div class="order-info">
                        <div class="order-number">#<?php echo esc_html($order->get_order_number()); ?></div>
                        <div class="order-date"><?php echo esc_html($order->get_date_created()->date_i18n('d M Y')); ?></div>
                    </div>
                    <div class="order-status">
                        <span class="status-badge status-<?php echo esc_attr($order->get_status()); ?>">
                            <?php echo esc_html(wc_get_order_status_name($order->get_status())); ?>
                        </span>
                    </div>
                    <div class="order-total">
                        <?php echo $order->get_formatted_order_total(); ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Recent Wishlist -->
        <?php if (!empty($recent_wishlist_products)) : ?>
        <div class="dashboard-section recent-wishlist">
            <div class="section-header">
                <h3><?php esc_html_e('İstek Listem', 'woo-custom'); ?></h3>
                <a href="<?php echo esc_url(wc_get_account_endpoint_url('wishlist')); ?>" class="view-all-link">
                    <?php esc_html_e('Tümünü Gör', 'woo-custom'); ?>
                </a>
            </div>
            <div class="wishlist-products">
                <?php foreach ($recent_wishlist_products as $product) : ?>
                <div class="wishlist-item">
                    <div class="product-image">
                        <a href="<?php echo esc_url($product->get_permalink()); ?>">
                            <?php echo $product->get_image('woocommerce_gallery_thumbnail'); ?>
                        </a>
                    </div>
                    <div class="product-details">
                        <h4 class="product-name">
                            <a href="<?php echo esc_url($product->get_permalink()); ?>">
                                <?php echo esc_html($product->get_name()); ?>
                            </a>
                        </h4>
                        <div class="product-price"><?php echo $product->get_price_html(); ?></div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Recent Reviews -->
        <?php if (!empty($recent_reviews)) : ?>
        <div class="dashboard-section recent-reviews">
            <div class="section-header">
                <h3><?php esc_html_e('Son Değerlendirmelerim', 'woo-custom'); ?></h3>
                <a href="<?php echo esc_url(wc_get_account_endpoint_url('my-reviews')); ?>" class="view-all-link">
                    <?php esc_html_e('Tümünü Gör', 'woo-custom'); ?>
                </a>
            </div>
            <div class="reviews-list">
                <?php foreach ($recent_reviews as $review) : 
                    $comment = $review['comment'];
                    $product = $review['product'];
                    $rating = $review['rating'];
                ?>
                <div class="review-item">
                    <div class="review-product">
                        <div class="product-image">
                            <?php echo $product->get_image('woocommerce_gallery_thumbnail'); ?>
                        </div>
                        <div class="product-info">
                            <h4 class="product-name"><?php echo esc_html($product->get_name()); ?></h4>
                            <div class="review-rating">
                                <?php echo $reviews_handler->get_star_rating_html($rating); ?>
                            </div>
                        </div>
                    </div>
                    <div class="review-content">
                        <?php echo wp_trim_words($comment->comment_content, 15, '...'); ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Quick Actions -->
    <div class="dashboard-actions">
        <h3><?php esc_html_e('Hızlı İşlemler', 'woo-custom'); ?></h3>
        <div class="action-buttons">
            <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" class="action-btn primary">
                <span class="btn-icon">🛍️</span>
                <?php esc_html_e('Alışverişe Devam Et', 'woo-custom'); ?>
            </a>
            <a href="<?php echo esc_url(wc_get_account_endpoint_url('edit-address')); ?>" class="action-btn secondary">
                <span class="btn-icon">📍</span>
                <?php esc_html_e('Adreslerimi Düzenle', 'woo-custom'); ?>
            </a>
            <a href="<?php echo esc_url(wc_get_account_endpoint_url('edit-account')); ?>" class="action-btn secondary">
                <span class="btn-icon">⚙️</span>
                <?php esc_html_e('Hesap Bilgilerim', 'woo-custom'); ?>
            </a>
        </div>
    </div>
</div>

<?php
/**
 * My Account dashboard hook - safe to call since we're using template override
 *
 * @since 2.6.0
 */
if (!defined('WOO_CUSTOM_DASHBOARD_LOADING')) {
    do_action('woocommerce_account_dashboard');
}
?>
