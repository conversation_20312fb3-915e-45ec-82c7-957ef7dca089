<?php
/**
 * My Account - My Reviews
 *
 * @package WooCustom
 */

defined('ABSPATH') || exit;

$reviews_handler = WooCustom_Reviews::instance();
$user_reviews = $reviews_handler->get_user_reviews();
$reviews_count = $reviews_handler->get_user_reviews_count();
$average_rating = $reviews_handler->get_user_average_rating();
$rating_distribution = $reviews_handler->get_user_rating_distribution();
?>

<div class="woo-custom-my-reviews">
    <div class="reviews-header">
        <h2><?php esc_html_e('Değerlendirmelerim', 'woo-custom'); ?></h2>
        
        <?php if ($reviews_count > 0) : ?>
            <div class="reviews-summary">
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo esc_html($reviews_count); ?></span>
                        <span class="stat-label"><?php esc_html_e('Toplam Değerlendirme', 'woo-custom'); ?></span>
                    </div>

                    <?php if ($average_rating > 0) : ?>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo esc_html($average_rating); ?></span>
                            <span class="stat-label"><?php esc_html_e('Ortalama Puan', 'woo-custom'); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if ($average_rating > 0) : ?>
                    <div class="rating-overview">
                        <div class="average-rating">
                            <?php echo $reviews_handler->get_star_rating_html($average_rating); ?>
                            <span class="rating-text"><?php echo esc_html($average_rating); ?> / 5</span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <?php if (!empty($user_reviews)) : ?>
        <div class="reviews-list">
            <?php foreach ($user_reviews as $review) : 
                $comment = $review['comment'];
                $product = $review['product'];
                $rating = $review['rating'];
                $verified = $review['verified'];
                $date = $review['date'];
            ?>
                <div class="review-item">
                    <div class="review-header">
                        <div class="product-info">
                            <div class="product-thumbnail">
                                <a href="<?php echo esc_url($product->get_permalink()); ?>">
                                    <?php echo $product->get_image('woocommerce_gallery_thumbnail'); ?>
                                </a>
                            </div>
                            
                            <div class="product-details">
                                <h4 class="product-name">
                                    <a href="<?php echo esc_url($product->get_permalink()); ?>">
                                        <?php echo esc_html($product->get_name()); ?>
                                    </a>
                                </h4>
                                
                                <div class="review-meta">
                                    <?php if ($rating > 0) : ?>
                                        <div class="review-rating">
                                            <?php echo $reviews_handler->get_star_rating_html($rating); ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="review-date">
                                        <?php echo esc_html($reviews_handler->format_review_date($date)); ?>
                                    </div>
                                    
                                    <?php if ($verified) : ?>
                                        <span class="verified-purchase">
                                            <span class="verified-icon">✓</span>
                                            <?php esc_html_e('Doğrulanmış Satın alma', 'woo-custom'); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="review-content">
                        <div class="review-text">
                            <?php echo wp_kses_post(wpautop($comment->comment_content)); ?>
                        </div>
                    </div>
                    
                    <div class="review-actions">
                        <a href="<?php echo esc_url($product->get_permalink() . '#comment-' . $comment->comment_ID); ?>"
                           class="view-review-link">
                            <?php esc_html_e('Ürün Sayfasında Görüntüle', 'woo-custom'); ?>
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else : ?>
        <div class="reviews-empty">
            <p><?php esc_html_e('Henüz hiç değerlendirme yazmadınız.', 'woo-custom'); ?></p>
            <p><?php esc_html_e('Ürün satın alın ve deneyimlerinizi diğer müşterilerle paylaşın!', 'woo-custom'); ?></p>
            <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" class="button">
                <?php esc_html_e('Alışverişe Başla', 'woo-custom'); ?>
            </a>
        </div>
    <?php endif; ?>
</div>

<style>
.woo-custom-my-reviews .reviews-header {
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border-left: 4px solid #007cba;
}

.woo-custom-my-reviews .reviews-header h2 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
}

.reviews-summary {
    background: #fff;
    padding: 25px;
    border-radius: 8px;
    margin-top: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e1e5e9;
}

.summary-stats {
    display: flex;
    gap: 40px;
    margin-bottom: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    min-width: 120px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.stat-number {
    display: block;
    font-size: 28px;
    font-weight: 700;
    color: #007cba;
    margin-bottom: 8px;
}

.stat-label {
    display: block;
    font-size: 13px;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.rating-overview {
    text-align: center;
}

.average-rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.rating-text {
    font-size: 18px;
    font-weight: bold;
}

.reviews-list {
    margin-top: 30px;
}

.review-item {
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
}

.review-header {
    margin-bottom: 15px;
}

.product-info {
    display: flex;
    gap: 15px;
}

.product-thumbnail {
    flex-shrink: 0;
}

.product-thumbnail img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 3px;
}

.product-details {
    flex: 1;
}

.product-name {
    margin: 0 0 10px 0;
    font-size: 16px;
}

.product-name a {
    text-decoration: none;
    color: #333;
}

.product-name a:hover {
    color: #0073aa;
}

.review-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.woo-custom-star-rating {
    display: flex;
    gap: 2px;
}

.woo-custom-star-rating .star {
    color: #ddd;
    font-size: 16px;
}

.woo-custom-star-rating .star.filled {
    color: #ffc107;
}

.review-date {
    color: #666;
    font-size: 14px;
}

.verified-purchase {
    background: #7ad03a;
    color: white;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.verified-icon {
    font-weight: bold;
}

.review-content {
    margin-bottom: 15px;
}

.review-text {
    line-height: 1.6;
    color: #333;
}

.review-actions {
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.view-review-link {
    color: #0073aa;
    text-decoration: none;
    font-size: 14px;
}

.view-review-link:hover {
    text-decoration: underline;
}

.reviews-empty {
    text-align: center;
    padding: 40px 20px;
    background: #f8f8f8;
    border-radius: 5px;
}

.reviews-empty p {
    margin-bottom: 15px;
    color: #666;
}

.reviews-empty .button {
    margin-top: 20px;
}

@media (max-width: 768px) {
    .summary-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .product-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .product-thumbnail {
        align-self: center;
    }
    
    .review-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .average-rating {
        flex-direction: column;
        gap: 5px;
    }
}
</style>
