/**
 * Woo Custom Plugin Styles
 */

/* Modern My Account Sidebar Styles */
.woocommerce-account .woocommerce-MyAccount-navigation {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e5e9;
    margin-bottom: 30px;
    width: 240px;
    flex-shrink: 0;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li {
    margin: 0 0 8px 0;
    padding: 0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li:last-child {
    margin-bottom: 0;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li a {
    display: block;
    padding: 12px 16px;
    color: #495057;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid transparent;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li a:hover {
    background: rgba(255, 255, 255, 0.9);
    color: #007cba;
    border-color: rgba(0, 124, 186, 0.2);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 124, 186, 0.15);
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.is-active a {
    background: #007cba;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(0, 124, 186, 0.3);
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.is-active a:hover {
    background: #005a87;
    transform: translateX(0);
    color: white;
}

/* My Account Layout Fix */
.woocommerce-account .woocommerce {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.woocommerce-account .woocommerce-MyAccount-content {
    flex: 1;
    min-width: 0;
}

/* Active menu item icons - make them white */
.woocommerce-account .woocommerce-MyAccount-navigation ul li.is-active a:before {
    color: white !important;
}

/* Ensure all menu item icons are properly styled */
.woocommerce-account .woocommerce-MyAccount-navigation ul li a:before {
    margin-right: 8px;
    font-size: 16px;
    width: 20px;
    text-align: center;
    display: inline-block;
}

/* Enhanced Dashboard Styles */
.woo-custom-dashboard {
    max-width: 100%;
}

.dashboard-welcome {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    text-align: center;
    border: 1px solid #e1e5e9;
}

.dashboard-welcome h2 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 28px;
    font-weight: 600;
}

.welcome-text {
    color: #666;
    font-size: 16px;
    margin: 0;
    line-height: 1.5;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    position: relative;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 124, 186, 0.1);
    border-radius: 50%;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    line-height: 1;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.stat-link {
    color: #007cba;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    position: absolute;
    top: 15px;
    right: 20px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.stat-link:hover {
    background: rgba(0, 124, 186, 0.1);
    color: #005a87;
}

.dashboard-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.dashboard-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e5e9;
    overflow: hidden;
}

.section-header {
    padding: 20px 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.view-all-link {
    color: #007cba;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.view-all-link:hover {
    background: rgba(0, 124, 186, 0.1);
    color: #005a87;
}

.orders-list, .wishlist-products, .reviews-list {
    padding: 20px 25px;
}

.order-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    border-bottom: 1px solid #f1f3f4;
}

.order-item:last-child {
    border-bottom: none;
}

.order-info {
    flex: 1;
}

.order-number {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.order-date {
    color: #666;
    font-size: 13px;
    margin-top: 2px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-processing {
    background: #fff3cd;
    color: #856404;
}

.status-on-hold {
    background: #f8d7da;
    color: #721c24;
}

.order-total {
    font-weight: 600;
    color: #333;
    margin-left: 15px;
}

.wishlist-item, .review-item {
    display: flex;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #f1f3f4;
}

.wishlist-item:last-child, .review-item:last-child {
    border-bottom: none;
}

.product-image {
    flex-shrink: 0;
}

.product-image img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 6px;
}

.product-details, .product-info {
    flex: 1;
}

.product-name {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 500;
}

.product-name a {
    color: #333;
    text-decoration: none;
}

.product-name a:hover {
    color: #007cba;
}

.product-price {
    color: #007cba;
    font-weight: 600;
    font-size: 14px;
}

.review-product {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
}

.review-content {
    color: #666;
    font-size: 13px;
    line-height: 1.4;
}

.dashboard-actions {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e5e9;
    text-align: center;
}

.dashboard-actions h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
}

.action-btn.primary {
    background: #007cba;
    color: white;
}

.action-btn.primary:hover {
    background: #005a87;
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #e1e5e9;
}

.action-btn.secondary:hover {
    background: #e9ecef;
    color: #333;
    transform: translateY(-1px);
}

.btn-icon {
    font-size: 16px;
}

@media (max-width: 768px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .dashboard-sections {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .action-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }

    /* Mobile layout for my account */
    .woocommerce-account .woocommerce {
        flex-direction: column;
        gap: 20px;
    }

    .woocommerce-account .woocommerce-MyAccount-navigation {
        width: 100%;
    }
}

/* Modern Addresses Page Styles */
.woocommerce-account .woocommerce-Addresses {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.woocommerce-account .woocommerce-Address {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e5e9;
    overflow: hidden;
    transition: all 0.3s ease;
}

.woocommerce-account .woocommerce-Address:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.woocommerce-account .woocommerce-Address-title {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 25px;
    margin: 0;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    gap: 10px;
}

.woocommerce-account .woocommerce-Address-title h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
    flex: 1;
}

.woocommerce-account .woocommerce-Address-title:before {
    content: "📍";
    font-size: 20px;
}

.woocommerce-account .woocommerce-Address address {
    padding: 25px;
    margin: 0;
    font-style: normal;
    line-height: 1.6;
    color: #495057;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.woocommerce-account .woocommerce-Address .edit {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 20px;
    margin: 0 25px 25px;
    background: #007cba;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
}

.woocommerce-account .woocommerce-Address .edit:hover {
    background: #005a87;
    transform: translateY(-1px);
    color: white;
}

.woocommerce-account .woocommerce-Address .edit:before {
    content: "✏️";
    font-size: 14px;
}

/* Empty address state */
.woocommerce-account .woocommerce-Address address:empty:before {
    content: "Henüz adres eklenmemiş";
    color: #999;
    font-style: italic;
    text-align: center;
    display: block;
}

/* Address form styles */
.woocommerce-account .woocommerce-address-fields {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e5e9;
}

.woocommerce-account .woocommerce-address-fields h3 {
    margin: 0 0 25px 0;
    color: #333;
    font-size: 20px;
    font-weight: 600;
    padding-bottom: 15px;
    border-bottom: 2px solid #e1e5e9;
}

.woocommerce-account .woocommerce-address-fields .form-row {
    margin-bottom: 20px;
}

.woocommerce-account .woocommerce-address-fields label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.woocommerce-account .woocommerce-address-fields input,
.woocommerce-account .woocommerce-address-fields select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.woocommerce-account .woocommerce-address-fields input:focus,
.woocommerce-account .woocommerce-address-fields select:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
}

.woocommerce-account .woocommerce-address-fields button {
    background: #007cba;
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.woocommerce-account .woocommerce-address-fields button:hover {
    background: #005a87;
    transform: translateY(-1px);
}

/* Wishlist Button Styles */
.woo-custom-wishlist-btn {
    position: relative;
    background: none;
    border: none;
    cursor: pointer;
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Heart icon styles */
.woo-custom-wishlist-btn .heart-icon,
.woo-custom-wishlist-btn .heart-icon-filled {
    font-size: 20px;
    transition: all 0.3s ease;
    display: inline-block;
}

.woo-custom-wishlist-btn .heart-icon {
    color: #999;
}

.woo-custom-wishlist-btn .heart-icon-filled {
    color: #e74c3c;
    display: none;
}

/* Hover states */
.woo-custom-wishlist-btn:hover .heart-icon {
    color: #e74c3c;
    transform: scale(1.1);
}

/* Active state (in wishlist) */
.woo-custom-wishlist-btn.in-wishlist .heart-icon {
    display: none;
}

.woo-custom-wishlist-btn.in-wishlist .heart-icon-filled {
    display: inline-block;
    animation: heartBeat 0.6s ease-in-out;
}

/* Heart beat animation */
@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1); }
    75% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Product loop positioning */
.woocommerce ul.products li.product .woo-custom-wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.woocommerce ul.products li.product:hover .woo-custom-wishlist-btn {
    opacity: 1;
    transform: translateY(0);
}

/* Single product page wishlist button */
.woo-custom-wishlist-wrapper {
    margin: 15px 0;
}

.woo-custom-wishlist-btn.single-product {
    background: none;
    border: none;
    padding: 0;
    border-radius: 0;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #333;
    text-decoration: none;
    cursor: pointer;
    margin-left: 30px;
    white-space: nowrap;
    width: 110px;
    outline: none;
}

.woo-custom-wishlist-btn.single-product:focus {
    outline: none;
    box-shadow: none;
}

.woo-custom-wishlist-btn.single-product:hover {
    background: none;
    color: #333;
    border: none;
}

.woo-custom-wishlist-btn.single-product:hover .heart-icon {
    color: #e74c3c;
}

.woo-custom-wishlist-btn.single-product.in-wishlist {
    background: none;
    color: #333;
    border: none;
}

.woo-custom-wishlist-btn.single-product .wishlist-text {
    font-weight: 500;
    margin-left: 4px;
}

.woo-custom-wishlist-btn.single-product .heart-icon,
.woo-custom-wishlist-btn.single-product .heart-icon-filled {
    font-size: 18px;
    margin-right: 4px;
}

/* Loading state */
.woo-custom-wishlist-btn.loading {
    opacity: 0.6;
    pointer-events: none;
}

.woo-custom-wishlist-btn.loading .heart-icon,
.woo-custom-wishlist-btn.loading .heart-icon-filled {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Login required state */
.woo-custom-wishlist-btn.requires-login {
    opacity: 0.7;
}

.woo-custom-wishlist-btn.requires-login:hover {
    opacity: 0.9;
}

/* Notification styles */
.woo-custom-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 9999;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.woo-custom-notification.show {
    opacity: 1;
    transform: translateX(0);
}

.woo-custom-notification.success {
    background: #27ae60;
}

.woo-custom-notification.error {
    background: #e74c3c;
}

/* My Account menu item icons - More specific selectors */
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:before,
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:before {
    display: inline-block !important;
    margin-left: 4px !important;
    text-align: center !important;
    line-height: 24px !important;
    font-size: 20px !important;
    vertical-align: middle !important;
    font-style: normal !important;
    font-weight: normal !important;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:before {
    content: "♡" !important;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:before {
    content: "☆" !important;
}

/* Remove default icons for other menu items - keep only wishlist and reviews */

/* Hover effects for menu icons */
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:hover:before {
    content: "♥" !important;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:hover:before {
    content: "★" !important;
}

/* Active state icons - white color for wishlist and reviews only */
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist.is-active a:before {
    content: "♥" !important;
    color: white !important;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews.is-active a:before {
    content: "★" !important;
    color: white !important;
}



/* Responsive design */
@media (max-width: 768px) {
    .woocommerce ul.products li.product .woo-custom-wishlist-btn {
        opacity: 1;
        transform: translateY(0);
        top: 5px;
        right: 5px;
        padding: 6px;
    }
    
    .woo-custom-wishlist-btn .heart-icon,
    .woo-custom-wishlist-btn .heart-icon-filled {
        font-size: 18px;
    }
    
    .woo-custom-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }
    
    .woo-custom-notification.show {
        transform: translateY(0);
    }
}

/* Product grid compatibility */
.woocommerce ul.products li.product {
    position: relative;
}

/* Theme compatibility adjustments */
.woocommerce ul.products li.product .woo-custom-wishlist-btn {
    z-index: 10;
}

/* Ensure proper positioning in different themes */
.products .product .woo-custom-wishlist-btn,
.wc-block-grid__products .wc-block-grid__product .woo-custom-wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
}

/* WooCommerce Blocks compatibility */
.wc-block-grid__product {
    position: relative;
}

.wc-block-grid__product .woo-custom-wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transition: all 0.3s ease;
}

.wc-block-grid__product:hover .woo-custom-wishlist-btn {
    opacity: 1;
}

/* Accessibility improvements */
.woo-custom-wishlist-btn:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

.woo-custom-wishlist-btn[aria-pressed="true"] .heart-icon {
    display: none;
}

.woo-custom-wishlist-btn[aria-pressed="true"] .heart-icon-filled {
    display: inline-block;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .woo-custom-wishlist-btn {
        border: 2px solid currentColor;
    }
    
    .woo-custom-wishlist-btn .heart-icon {
        color: currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .woo-custom-wishlist-btn,
    .woo-custom-wishlist-btn .heart-icon,
    .woo-custom-wishlist-btn .heart-icon-filled,
    .woo-custom-notification {
        transition: none;
        animation: none;
    }
}
